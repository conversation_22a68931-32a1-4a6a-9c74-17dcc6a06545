import * as hub from 'langchain/hub/node'
import logger from 'model/logger/logger'
import { ChatHistoryService } from '../chat_history/chat_history'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { IWorkflowState } from '../llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { Runnable } from '@langchain/core/runnables'

export class FreeThink {
  private _freeThinkPrompt: Runnable | null = null
  private chatHistoryServiceClient: ChatHistoryService
  private eventTrackClient: EventTracker
  constructor(chatHistoryServiceClient: ChatHistoryService, eventTrackClient: EventTracker) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.eventTrackClient = eventTrackClient
  }

  private async getFreeThinkPrompt(): Promise<Runnable> {
    if (!this._freeThinkPrompt) { this._freeThinkPrompt = await hub.pull('free-think') }
    return this._freeThinkPrompt
  }

  public async invoke(state: IWorkflowState, thinkPrompt: string, metaActions: string, customerBehavior: string, customerPortrait: string, temporalInformation: string) {
    const freeThinkPrompt = await this.getFreeThinkPrompt()
    const randomTemperature = Number((Math.random() * 0.3 + 0.7).toFixed(2))
    const dialogHistory = await this.chatHistoryServiceClient.getChatHistory(state.chat_id, 6, 18)
    await state.interruptHandler.interruptCheck()

    const output = await LLM.predict(
      freeThinkPrompt, {
        temperature: randomTemperature,
        response_json: true,
        meta: {
          promptName: 'free_think',
          chat_id: state.chat_id,
          round_id: state.round_id,
        } }, {
        thinkPrompt: thinkPrompt,
        metaActions: metaActions,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      })

    let think: string = ''
    let action: string[] = []
    let strategy: string = '正常回复'
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      action = parsedOutput.action
      strategy = parsedOutput.strategy
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeThink 解析 JSON 失败:', error)
    }

    this.eventTrackClient.track(state.chat_id, IEventType.FreeThink, { round_id: state.round_id, think: think, action: JSON.stringify(action), strategy: strategy, content: content })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${JSON.stringify(action)}\nstrategy: ${strategy}`)
    return { think, action, strategy, content }
  }
}