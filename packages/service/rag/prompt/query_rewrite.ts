import { PromptTemplate } from '@langchain/core/prompts'

export class QueryRewritePrompt {
  public static async format(query: string, chat_history: string) {
    const template = `# 意图改写
您的任务是为搜索引擎提供一个更合适的意图改写，以回答给定的问题。请将每个推断的问题简洁地用<query></query>标签括起来

{chat_history}

客户查询：
{query}

要求:
- 优先根据客户的原始问题生成推断问题，不偏离其语义。必要时，可以使用上下文来细化或增强问题，但不得改变原问题的核心意图。
- 仅在客户问题过于模糊时，使用上下文来补充和澄清问题。如果历史聊天与客户查询无关，请专注于客户查询，不要插入历史聊天信息中的无关内容。
- 从聊天记录中识别出主要的主题、话题或问题（例如：“系统班”“冥想入门营”“情绪问题”“能量测评”）。
- 专有名词：能量测评，财富果园，冥想入门营，21天系统班，当遇到专有名词时，无需增加其他的定语辅助描述。
- 在所有推断问题中避免使用代词或模糊地引用（例如：“这个”“那个”“这些”“那些”）。始终用具体、明确的主题或问题替换。
- 生成 1 个有洞察力的改写查询，并放在标签 <query></query> 中。
- 用中文回答，只需要输出结果（用标签 <query></query> 包括的改写查询），不要输出其他内容。

意图改写：`

    const promptTemplate = PromptTemplate.fromTemplate(template)
    return promptTemplate.format({ chat_history, query })
  }
}
