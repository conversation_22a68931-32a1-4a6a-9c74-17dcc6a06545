export interface IActionInfo {
  guidance: string;
  callback?: () => Promise<void>;
}

/**
 * 元行为组
 */
export abstract class MetaActionComponent {
  /**
   * 使用哪一组Meta action
   */
  public abstract getMetaAction(chatId: string): Promise<Record<string, string>>;

  /**
   * 使用什么thinkPrompt
   */
  public abstract getThinkPrompt(chatId: string): Promise<string>;

  /**
   * 能使用什么action
   */
  public abstract getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null>

  /**
   * 激活元行为组的条件
   * 注意，重复命名的action，调度逻辑会被后者覆盖
   */
  public abstract isStageActive(chatId: string): Promise<boolean>;

  /**
   * 元行为组激活后的附加信息
   * @param chatId
   */
  public abstract getGuidance(chatId: string): Promise<string>;

  /**
   * 使用元行为组前的处理逻辑
   * @param chatId
   * @param roundId
   */
  public abstract prepareActivation(chatId: string, roundId: string): Promise<void>;


}
