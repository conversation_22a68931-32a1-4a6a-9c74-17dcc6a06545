export interface IDevelopConfig extends IModelConfig {
  polyv: {
    appId: string
    userId: string
    appSecret: string
  }

  xbb: { // 销帮帮
    token: string
  }

  baoshuConfig: {
    operationGroupId: string // 运营群 ID
  }

  xunfeiASR: {
    appId: string
    secretKey: string
  }

  bingSearch: {
    apiKey: string
  }

  elasticSearch: {
    url: string
    username: string
    password: string
  }


  langsmith: {
    apiKey: string
  }

  // 等待时间
  waitingTime: {
    messageMerge: number // 合并消息等待时间

    activePush: {
      interval: number
    }

    reAsk: {
      firstWaitingTime: number // 初始化全局等待时间
      pushGroup: number // 追问进群
      groupLinkNotClicked: number // 进群链接未点击
    }
  }

  messageCenter: {
    url: string
  }

  oss: {
    [key: string]: {
      bucket: string
      internal: boolean
      region: string
      domain: string
    }
  }

  apiCenter: {
    agentBaseUrl: string
  }

  sls: ISlsConfigure

  mongoDB: {
    wechat_db: IMongoDBConfig
    log_db: IMongoDBConfig
  }

  redis: {
    password: string
    url: string
  }

  juziWecom: {
    baseUrl: string
    token: string
  }

  yiwise: {
    appKey: string
    appSecret: string
    tenantSign: string
    version: string
  }

  moerWxBiz:{
    corpid: string
    corpsecret: string
  }

  siliconFlow: {
    apiKey: string
  }

  xingyan: {
    baseUrl: string
    'zhongshentong': {
      cropId: number
      secretKey: string
    }
    'zhicheng': {
      cropId: number
      secretKey: string
    }
  }

  whatsapp:{
    baseUrl: string
    apiKey: string
    tenantId: string
    mainAccountUserName: string
  }
}

interface IMongoDBConfig {
  uri: string
}

interface ISlsConfigure {
  account: {
    accessKeyId: string
    accessKeySecret: string
  }

  tokenApi: {
    endpoint: string
    apiVersion: string
  }

  speechRecognition: {
    //一句话识别
    url: string
    appKey: string
  }
}

export interface IEnvConfig {
  AGENT_ID: string
  BOT_NAME: string
  CREATE_AT: string
}

export interface IWechatConfig {
  orgToken?: string // 组织 token
  id: string // imContactId
  name: string
  botUserId: string // 企业微信 userId
  notifyGroupId: string // 通知群 ID

  classGroupId: string
  isGroupOwner?: boolean // 是否是群主，只有群主才可以在课程群发送消息

  proxyGroupNotify?: boolean // 使用代理通知
  createdTime?: Date // 挂号时间
}

interface IAdminTestConfig {
  ws?: any
}

// 大模型调用配置
export interface IModelConfig {
  openai: {
    apiKeys: string[]
    apiBaseUrl: string
  }

  azureOpenAI: {
    azureOpenAIApiKey: string
    azureOpenAIApiVersion: string
    azureOpenAIApiInstanceName: string
    azureOpenAIApiDeploymentName: string[]
    apiBaseUrl: string
  }

  // 通义千问
  qwen: {
    apiKey: string
  }

  // 三方 API
  cheapOpenAI: {
    apiBaseUrl: string
    apiKey: string
  }

  stableClaude: {
    apiKey: string
    apiBaseUrl: string
  }
}

export interface IMoerLesson {
  vodId: number         // VOD ID
  liveId: number        // 直播 ID
  day: number           // 资源对应的天数
  liveShortUrl: string      // 资源的短链接
  vodShortUrl: string       // 资源的短链接
  resourceName: string  // 资源名称
  resourceType: number  // 资源类型
}

export interface IConfig extends IDevelopConfig, IEnvConfig, IAdminTestConfig {
  bindingPhone?: boolean
  eventForward?: boolean // 消息转发服务

  courseInfo?: {
    no: number
    name: string
    startTime: Date
    lessons: IMoerLesson[]
  }

  onlyReceiveMessage?: boolean // 是否只接收消息, 不进行回复
  whitelist?: string[]

  wechatConfig?: IWechatConfig
  localTest?: boolean
  startTime?:number

  enterpriseName?: string // 企业名称，需要跟 bot_config 中的名称一致
  projectName?:string
  dbName?:string
}
