import { DataService } from '../get_data'
import dayjs from 'dayjs'
import { Config } from 'config'

describe('get_data_test', () => {

  it('getCurrentTimeTest', async () => {
    console.log(await DataService.getCurrentTime('7881301539017951_1688858335726355'))
    console.log(dayjs().hour())
  }, 60000)

  it('getCurrentCourseNo', async () => {
    console.log(DataService.getCurrentCourseNo())
  }, 60000)

  it('getCourseStartTime', async () => {
    console.log(DataService.getCourseStartTime(20250520).toLocaleString())
  }, 60000)

  it('getCourseInfoByCourseNo', async () => {
    console.log(await DataService.getCourseInfoByCourseNo(20250704))
  }, 60000)

  it('getSystemCourseStartTime', async () => {
    const startTime = await DataService.getSystemCourseStartTime()
    console.log('系统陪跑班最早开课时间:', startTime)
    expect(startTime).toBeTruthy()
  }, 60000)

  it('', async () => {
    console.log(dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
    console.log(dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'))
  }, 60000)

  it('isInCourseTimeLine', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 3,
        time: '21:00:00',
      }
    }
    console.log(await DataService.isInCourseTimeLine('11111', 'inCourse', 3))
  })

  it('获取手机号', async () => {
    // console.log(JSON.stringify(await JuziAPI.externalIdToWxId('wmfFqnVQAAIbohzDncMFzjQ9HM9ppGoQ', '1688858213716953'), null, 4))

    const phoneNumber = await DataService.findPhoneNumberByRemark(
      '7881302189911948',
      '1688858213716953',
    )

    console.log(!phoneNumber)
  }, 60000)

  it('delay courseNo', async() => {
    Config.setting.enterpriseName = 'yuhe'
    await DataService.delayCourseNo('7881302146051227_1688858335726355', 20250611)
  }, 600000)

})