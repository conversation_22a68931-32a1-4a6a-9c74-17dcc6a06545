import { Config } from 'config'
import { ObjectUtil } from 'lib/object'
import logger from 'model/logger/logger'

export enum TeacherName {
  ZST = '中神通',
  XW = '小王',
  YJ = '玉姐',
}

export const accountToName:Record<string, string> = {
  '****************': '陆文都',
  '****************': '团队-大麦',
  '****************': '柒越小号',
  '****************': '团队~星河',
  '****************': '团队-柒越',
  '****************': '团队-星旭',
  '****************': '团队-星越',
  '****************': '团队-星光',
  '****************': '团队-星晨',
  '****************': '团队-星月',
  '****************': '团队—星愿',
  '****************': '团队-星星',
  '****************': '团队—星云',
  '****************': '团队-星晨',
  '****************': '团队—星悦',
  '****************': '团队-思思',
  '****************': '团队-小多',
  '****************': '团队-多米',
  '****************': '团队-小锋',
  '****************': '团队-小余',
  '****************': '团队-月光',
  '****************': '团队—星乐',
  '****************': '团队—月亮',
  '****************': '团队—星空',
  '*************015': '团队-星玥',
  '1688858338695008': '团队-夏天',
  '1688856476728639': '团队-星晴',
  '1688856274751732': '团队-秋天',
  '1688857543771488': '团队-星宇',
  '1688858041741595': '团队-星海',
  '1688857031673243': '团队-柒星',
  '1688856382598119': '团队—柒柒',
  '1688854711638484': '团队—成成',
  '1688856379707705': '团队-海星',
  '1688856555724523': '团队-彤彤',
  '1688855475747551': '团队-星耀'
}

export class YuHeConfig extends Config {
  public static getTeacherName() {
    const teacherName = process.env.TEACHER_NAME

    if (teacherName) {
      if (!(ObjectUtil.enumValues(TeacherName) as string[]).includes(teacherName)) {
        logger.warn(`${teacherName} 应该为 ${ObjectUtil.enumValues(TeacherName).join(',')} 之一`)
        return TeacherName.XW
      }

      return teacherName as TeacherName
    }

    return TeacherName.XW
  }
}