import axios from 'axios'
import { Config } from 'config'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { YuHeValidator } from '../helper/validator/validator'
import { DataService } from '../helper/getter/get_data'
import { IChattingFlag } from '../state/user_flags'
import { Queue } from 'bullmq'
import { RedisDB } from 'model/redis/redis'
import { JuziAPI } from 'model/juzi/api'
import { FileHelper } from 'lib/file'
import path from 'path'
import { chatStateStoreClient } from '../service/instance'
import { PrismaMongoClient } from 'model/mongodb/prisma'

describe('Test', function () {
  beforeAll(() => {

  })

  it('', async () => {
    // yuhe_import_white_list
    const queue = new Queue('yuhe_import_white_list', {
      connection: RedisDB.getInstance()
    })

    // await queue.add(
    //   'checkPrevDay',
    //   { only_check: true, prevDay: true },
    //   {
    //     repeat: { pattern: '0 12 * * *' }, // 每天中午12点执行
    //     jobId: 'checkPrevDayJob1',
    //   }
    // )

    await queue.add(
      'checkPrevDay',
      { },
      {
        repeat: { pattern: '0 18 * * *' }, // 每天下午6点执行
        // jobId: 'checkPrevDayJob2',
      }
    )

    console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))

    // await queue.add(
    //   'dailyImportWhiteList',
    //   { timestamp: Date.now() },
    //   {
    //     repeat: { pattern: '0 0 * * *' }, // 每天 0 点执行
    //     jobId: 'dailyImportWhiteList', // 固定的 jobId 确保只有一个任务
    //   }
    // )
  }, 60000)

  it('获取现有账号对应关系', async () => {
    // 获取微信
    const res = await JuziAPI.listAccounts('6801c818f8b463b1d228f23d')
    const accounts = res.data

    const existingAccount = {}

    // 拉一下 config, 根据 ID 做下去重
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'yuhe'
        }
      }
    )

    for (const account of accounts) {
      const config = configs.find((config) => config.wechatId === account.wxid)

      if (config) {
        // // @ts-ignore fku
        // existingAccount[config.accountName as string] = account.nickName

        await PrismaMongoClient.getConfigInstance().config.update({
          where: {
            id: config.id
          },
          data: {
            nickName: account.nickName
          }
        })
      }

    }

    console.log(JSON.stringify(existingAccount, null, 4))

  }, 30000)

  it('获取新账号信息', async () => {
    // 获取微信
    const res = await JuziAPI.listAccounts('6801c818f8b463b1d228f23d')
    const accounts = res.data

    // 拉一下 config, 根据 ID 做下去重
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'yuhe'
        }
      }
    )

    const toAddAccounts: any[] = []

    for (const account of accounts) {
      const config = configs.find((config) => config.wechatId === account.wxid)
      if (!account.wxid) {
        continue
      }

      if (!config) {
        toAddAccounts.push(account)
      }
    }

    console.log(JSON.stringify(toAddAccounts, null, 4))
  }, 30000)

  it('添加账号配置', async () => {
    // 添加 docker 配置, 推送代码

    const configs = [
      //9597cb6c249920c882c609efcf8ac219(****************)
      //ChenXingXu(****************)
      // 企业账号是可以重叠的
      {
        'enterpriseName': 'yuhe',
        'accountName': 'yuhe21',
        'wechatId': '***************',
        'address': 'http://***************:5021',
        'port': '5021',
        'botUserId': 'WangKun',
        'orgToken': '6801c818f8b463b1d228f23d',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
        }
      },
      {
        'enterpriseName': 'yuhe',
        'accountName': 'yuhe22',
        'wechatId': '****************',
        'address': 'http://***************:5022',
        'port': '5022',
        'botUserId': 'TuanDuiXingLe',
        'orgToken': '6801c818f8b463b1d228f23d',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
        }
      }
    ]

    await PrismaMongoClient.getConfigInstance().config.createMany({
      data: configs
    })

    let appendDockerFile = ''
    // log docker 配置
    for (const config of configs) {
      appendDockerFile += `
  ${config.accountName}:
      image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe:latest
      container_name: ${config.accountName}
      environment:
      - NODE_ENV=dev
      - TEACHER_NAME=中神通
      - TZ=Asia/Shanghai
      - WECHAT_NAME=${config.accountName}
      ports:
        - "${config.port}:${config.port}"
      restart: always`
    }

    // 直接在文件尾部写入
    await FileHelper.appendFile(path.join(process.cwd(), 'apps', 'yuhe', 'docker', 'docker-compose.yaml'), appendDockerFile)

    const res = await axios.post('http://*************:6001/api/clear-server-address-cache')
    console.log(JSON.stringify(res.data, null, 4))

    // YuHe 事件转发缓存清除

    // 提醒提交代码
    console.log('提交代码，更新 docker 配置')
  }, 60000)

  it('导入白名单', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('****************') // 挂个配置，用于发送群通知消息
    Config.setting.dbName = 'yuhe'

    // // 1. 分组配置，校验直播间配置，进阶课配置
    // await YuHeValidator.validateLiveStream()

    // 2. 导入白名单
    await DataService.importWhiteListOfYesterdayUsers()
  }, 60000)

  it('ce', async () => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>('7881303126308651_****************')
    console.log(flags.is_in_live_room ?? false)
  }, 60000)


  it('123123213', async () => {
    await YuHeValidator.validateLiveStream()
  }, 30000)


})