import { DataService } from '../helper/getter/get_data'
import { Config } from 'config'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { importWhiteListOfDateUsers } from './silent_reask_bullmq.test'
import { XingyanAPI } from 'model/xingyan'
import { PrismaMongoClient } from '../database/prisma'

describe('import', () => {
  jest.setTimeout(6000000)
  test('import white list', async() => {
    const courseNo = 20250506
    const mongoClient = PrismaMongoClient.getInstance()
    const chatList = await mongoClient.chat.findMany({ where:{ id:'7881301465908299_1688858335726355', course_no:courseNo } })
    console.log(chatList.filter((chat) => !chat.phone).map((chat) => chat.id).join('\n'))
    for (const chat of chatList.filter((chat) => chat.phone)) {
      if (!chat.phone) continue
      console.log(chat.contact.wx_name, chat.phone)
      const botId = chat.wx_id
      const xingYanClient:XingyanAPI = XingyanAPI.getXingYanClientByBotId(botId)
      await DataService.addBoardcastWhiteList(xingYanClient, courseNo, chat.contact.wx_name, chat.phone)
    }
  })

  it('YuHeEventHandler', async () => {
    // await new YuHeEventHandler().handleProductPurchase({
    //   'param': {
    //     'account': 'visitor_EeBd2r',
    //     'mobile': '',
    //     'nickname': 'SYQ',
    //     'payAmt': '0.01',
    //     'payMethod': 1,
    //     'payTime': '2025-05-09 15:43:51',
    //     'productId': ****************,
    //     'productName': 'test',
    //     'roomId': 0,
    //     'roomName': '',
    //     'userId': ****************
    //   },
    //   'pushTime': *************,
    //   'roomId': 0,
    //   'sign': 'cd7858f55f169b6f593db2fe42dd0301',
    //   'type': 4
    // })
  }, 60000)


  it('import white list123', async () => {
    Config.setting.localTest = false
    Config.setting.dbName = 'yuhe'

    Config.setting.wechatConfig = await loadConfigByWxId('****************')

    await DataService.importWhiteListOfYesterdayUsers()
  }, 1E8)

  it('拉一下 0711 这批客户', async () => {
    // const chats = await YuheDataService.getChatsByCourseNo(********)
    // // console.log(chats.map((chat) => chat.id).join('\n'))
    //
    // console.log(chats.length)

    await importWhiteListOfDateUsers(new Date('2025-07-11'))
  }, 30000)


})