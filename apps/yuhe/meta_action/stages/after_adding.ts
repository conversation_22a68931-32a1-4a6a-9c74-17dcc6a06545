import { IActionInfo, MetaActionComponent } from 'model/meta_action/meta_action_component'
import { PostAction } from './post_action'
import { DataService } from '../../helper/getter/get_data'
import { MetaAction } from '../meta_action'

export class AfterAdding extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const afterAdding = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1)
    return Promise.resolve(afterAdding)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发送成功案例': PostAction.sendCaseImage,
      '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaAction.metaActionsAfterAdding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(MetaAction.thinkPromptAfterAdding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

}