import { IActionInfo, MetaActionComponent } from 'model/meta_action/meta_action_component'
import { MetaAction } from '../meta_action'
import { DataService } from '../../helper/getter/get_data'

export class AfterPaid extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return Promise.resolve(isPaid)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaAction.metaActionsAfterPaid)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(MetaAction.thinkPromptAfterPaid)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }




}