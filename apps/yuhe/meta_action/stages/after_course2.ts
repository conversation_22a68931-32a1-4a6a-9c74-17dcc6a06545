import { IActionInfo, MetaActionComponent } from 'model/meta_action/meta_action_component'
import { PostAction } from './post_action'
import { DataService } from '../../helper/getter/get_data'
import { MetaAction } from '../meta_action'

export class AfterCourse2 extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const afterCourse2Sales = await DataService.isInCourseTimeLine(chatId, 'afterSales', 2)
    return Promise.resolve(afterCourse2Sales)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '保留名额': PostAction.reaskAnotherDay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaAction.metaActionsAfterCourse2)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(MetaAction.thinkPromptAfterCourse2)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

}