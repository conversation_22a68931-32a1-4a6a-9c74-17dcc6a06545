import { DataService } from '../../helper/getter/get_data'
import { MetaActionRouter } from '../meta_action_router'
import { PostAction } from '../stages/post_action'
import { chatStateStoreClient } from '../../service/instance'
import { PrismaMongoClient } from '../../database/prisma'


describe('metaActionTest', () => {
  it('testGetMetaAction', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 3,
        time: '23:38:00',
      }
    }
    DataService.isPaidSystemCourse = async () => {
      return true
    }


    const { thinkPrompt, metaActions } = await MetaActionRouter.getThinkAndMetaActions('7881299950922845_1688858213716953', '')
    console.log(thinkPrompt)
    console.log(metaActions)
  }, 9e8)


  it('测试MetaAction', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 3,
        time: '23:38:00',
      }
    }
    DataService.isPaidSystemCourse = async () => {
      return false
    }


    const { thinkPrompt, metaActions } = await MetaActionRouter.getThinkAndMetaActions('7881299950922845_1688858213716953', '')
    console.log(thinkPrompt)
    console.log(metaActions)

    console.log(await MetaActionRouter.handleAction('chatId', 'roundId', ['强推陪跑营服务']))

  }, 9e8)

  it('刷新延期状态', async () => {
    const expData = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no_ori: { isSet: true }
      }
    })

    // const expData = await PrismaMongoClient.getInstance().chat.findMany({
    //   where: {
    //     id: '7881300516060552_1688857404698934'
    //   }
    // })

    for (const item of expData) {
      console.log(item.id)
      const chatState = await chatStateStoreClient.get(item.id)
      chatState.state.has_postpone = true
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: item.id
        },
        data: {
          chat_state: chatState
        }
      })
    }

  }, 9e8)

  it('TestGetNewCourseStartDayDiff', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 13,
        time: '23:38:00',
      }
    }
    console.log(await PostAction.getNewCourseStartDayDiff('7881299950922845_1688858213716953'))
  }, 60000)


})