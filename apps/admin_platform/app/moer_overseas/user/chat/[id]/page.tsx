'use client'
import { ChatHistory } from '@/app/component/user/chat_history'
import { queryChatById } from '@/app/moer_overseas/api/chat'
import { queryChatHistoryByChatId } from '@/app/moer_overseas/api/chat_history'
import { createManyDashboardData, queryDashboardDataByChatId } from '@/app/moer_overseas/api/dashboard_data'
import { findOrCreateDashboardTag, queryAllDashboardTags } from '@/app/moer_overseas/api/dashboard_tag'
import { queryLogByChatId } from '@/app/moer_overseas/api/log_store'
import { use } from 'react'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory
    queryDashboardDataByChatId={queryDashboardDataByChatId}
    createManyDashboardData={createManyDashboardData}
    queryAllDashboardTags={queryAllDashboardTags}
    findOrCreateDashboardTag={findOrCreateDashboardTag}
    id={decodeURIComponent(id)}
    queryChatHistoryByChatId={queryChatHistoryByChatId}
    queryLogByChatId={queryLogByChatId}
    queryChatById={queryChatById}
    langsmithProjectId='06e01395-e9a2-41e9-8fea-ba3fed9aa3f0'
  />
}