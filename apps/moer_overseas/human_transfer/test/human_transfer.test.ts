import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { HumanTransfer, HumanTransferType } from '../human_transfer'
import { getUserId } from 'config/chat_id'


describe('humanTransferTest', () => {

  it('test_transfer', async () => {
    Config.setting.enterpriseName = 'moer_overseas'
    Config.setting.dbName = 'moer_overseas'
    Config.setting.wechatConfig = await loadConfigByAccountName('moer_whatsapp_online_test')
    const chatId = '+8617326651677_+***********'
    await HumanTransfer.transfer(chatId, getUserId(chatId), HumanTransferType.NotBindPhone, true)
  }, 60000)
})