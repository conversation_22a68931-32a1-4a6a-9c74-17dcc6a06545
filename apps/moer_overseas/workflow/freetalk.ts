import { ContextBuilder } from './context'
import { freeThinkClient } from '../service/instance'
import { IWorkflowState } from 'service/llm/state'
import { MetaActionRouter } from '../meta_action/meta_action_router'
import { Node } from './nodes/types'
import { Reply } from './reply'
import { trackInvoke, WorkFlowNode } from './nodes/base_node'
import { UserLanguage } from '../helper/language/user_language_verify'

export class FreeTalk extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(state.chat_id, state.round_id)
    const contextComponent = new ContextBuilder({ state })
    const { action, strategy } = await freeThinkClient.invoke(
      state,
      metaActionStage.thinkPrompt,
      metaActionStage.metaActions,
      await contextComponent.customerBehavior(state.chat_id),
      await contextComponent.customerPortrait(state.chat_id),
      await contextComponent.temporalInformation(state.chat_id),
    )

    const actionInfo = await MetaActionRouter.handleAction(state.chat_id, state.round_id, action)
    const languageOption = await UserLanguage.getLanguageSetting(state.chat_id)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      metaActionStage.guidance,
      actionInfo.guidance,
      languageOption
    ].filter(Boolean).join('\n')

    const context = await ContextBuilder.build({
      state,
      includeRAG: true,
      includeMemory: true,
      talkStrategyPrompt: talkStrategyPrompt,
    })

    await Reply.invoke({
      state,
      context: context,
      temperature: 0.8,
      max_tokens: 400,
      promptName: 'free_talk',
      postReplyCallBack: actionInfo.callback
    })
    return Node.FreeTalk
  }
}