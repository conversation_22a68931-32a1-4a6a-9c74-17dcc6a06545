import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from './type'
import { WealthOrchardStore } from 'service/local_cache/chat_state_store'
import { IChattingFlag } from '../state/user_flags'
import { AsyncLock } from 'model/lock/lock'
import { DataService } from '../helper/getter/get_data'
import { WhatsappMessageMedia } from 'service/message_handler/ycloud/message_sender'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient, yCloudMessageSender } from '../service/instance'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { UserLanguage } from '../helper/language/user_language_verify'
import { energyTestLink } from '../service/global_data'
import logger from 'model/logger/logger'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { getUserId } from 'config/chat_id'
import { SendWelcomeMessageTask } from '../client/handler/task/send_welcome_message_task'
import { Moer<PERSON><PERSON>asAP<PERSON> } from 'model/moer_overseas_api/moer_overseas_api'


export class TaskRegister {
  public static register() {
    SilentReAsk.registerTask(TaskName.sendEnergyTest, async (chat_id: string, params) => {
      if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_delayed_send_energy_test) {
        return
      }

      await chatStateStoreClient.update(chat_id, {
        state: {
          is_delayed_send_energy_test: true,
        }
      })

      const lock = new AsyncLock()
      await lock.acquire(chat_id, async () => {

        if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_send_energy_test) {
          return
        }

        const chatHistory = await chatHistoryServiceClient.getRecentConversations(chat_id, 6, 'assistant')
        const formatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)
        if (formatHistory.includes('https://jsj.top/f/cYLSSy')) {
          await chatStateStoreClient.update(chat_id, {
            state: {
              is_send_energy_test: true
            }
          })
          return
        }

        chatStateStoreClient.update(chat_id, {
          state: {
            is_send_energy_test: true
          }
        })

        // 做过能量测评就不再发送了
        const energyTestScore = await DataService.getEnergyTestScore(chat_id)
        if (!energyTestScore && energyTestScore !== 0) {
          const language = await UserLanguage.getLanguage(chat_id)
          let msg = ''
          if (language === UserLanguage.Language_EN) {
            msg = `Here’s the link to the energy assessment—go ahead and see how your current energy is doing.  
After you finish, just screenshot your results and send them to me; our class assistant will give you a personalized 1-on-1 interpretation.  
Simply choose whatever feels right to you at first glance!`
          } else if (language === UserLanguage.Language_ZH) {
            msg = `小講堂老師說的能量測評的使用連結在這裡。可以評估一下自己當下的能量狀態哦。做完可以截圖給我哈，班班會給咱們1V1解讀一下哈。（按照自己內心第一選擇就可以啦）${energyTestLink}`
          }


          await yCloudMessageSender.sendById({
            chat_id: chat_id,
            ai_msg: msg,
            type:'text'
          }, { shortDes:'[发送能量测评链接]' })

          await yCloudMessageSender.sendById({
            chat_id: chat_id,
            ai_msg: '[能量测评介绍图片]',
            type:'image',
            send_msg: <WhatsappMessageMedia>{
              link: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E8%83%BD%E9%87%8F%E6%B5%8B%E8%AF%84.png'
            }
          }, { shortDes:'[能量测评介绍图片]' })
        }

      }, { timeout: 3 * 60 * 1000 })
    })

    SilentReAsk.registerTask(TaskName.wealthOrchardCleanup, async (chat_id: string, params) => {
      WealthOrchardStore.clearUserMessages(chat_id)
    })

    SilentReAsk.registerTask(TaskName.bindPhone, async (chat_id: string, params) => {
      try {
        await MoerOverseasAPI.getUserByPhone(params.phoneNumber)
        await SendWelcomeMessageTask.sendWelcomeMessage(chat_id, params.phoneNumber, params.name)
      } catch (e) {
        await HumanTransfer.transfer(chat_id, getUserId(chat_id), HumanTransferType.NotBindPhone, true)
        await chatDBClient.setHumanInvolvement(chat_id, true)
      }
    })
  }
}