import { UserLanguage } from '../user_language_verify'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../../../service/instance'

describe('userLanguageVerifyTest', () => {
  it('testVerify', async () => {
    const sentenceCN = '老师，我要报名课程 It\'s 11:30 am here what is the time there now'
    const sentenceEN = '想参加五天冥想营 It\'s 11:30 am here what is the time there now'

    chatDBClient.create = (chat) => {
      return Promise.resolve(chat)
    }

    chatStateStoreClient.getNodeCount = (chat_id, name) => {
      return Promise.resolve(3)
    }

    chatHistoryServiceClient.getUserMessages = (chat_id, round) => {
      return Promise.resolve([sentenceEN])
    }

    await UserLanguage.verify('11')


    console.log(chatStateStoreClient.get('11'))
  }, 60000)
})