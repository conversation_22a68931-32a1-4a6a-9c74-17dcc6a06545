import { DateHelper } from 'lib/date/date'
import { DataService } from '../get_data'


describe('get_data', () => {
  it('test_date', () => {
    console.log(DateHelper.formatDate(new Date(), 'HH:mm:ss'))
  })

  it('test_getCourseStartTimeByCourseNo', async () => {
    console.log(await DataService.getCourseStartTimeByCourseNo(100))
  }, 60000)

  it('test_getCurrentWeekCourseNo', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)
})