import { ClientAccountConfig, loadConfigByAccountName } from 'service/database/config'
import { Config, MoerAccountType } from 'config'
import { DataService } from '../helper/getter/get_data'
import chalk from 'chalk'
import { ObjectUtil } from 'lib/object'
import { TaskRegister } from '../schedule/task_register'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { moerOverseasVisualizedSopProcessor } from '../service/instance'

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.BOT_NAME = '麦子老师'
  Config.setting.enterpriseName = 'moer_overseas'
  Config.setting.projectName = 'moer_overseas'
  Config.setting.dbName = 'moer_overseas'


  // 注入配置
  // 读取注入的 姓名
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  const currentCourseNo = DataService.getCurrentWeekCourseNo()

  Config.setting.wechatConfig = await loadConfigByAccountName(name)

  console.log(chalk.green(`当前账号：${account.nickname}(${account.wechatId}) ${ObjectUtil.enumValueToKey(MoerAccountType, Config.getAccountType())}`))

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  moerOverseasVisualizedSopProcessor.start()
}

