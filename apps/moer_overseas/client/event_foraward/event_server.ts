import express from 'express'
import { IMoerEvent } from '../client_server'
import logger from 'model/logger/logger'
import { MoerEventForwardHandler } from './moer_event_forward'
import { catchGlobalError } from 'model/server/server'
import { Config } from 'config'
import { Event } from 'service/message_handler/ycloud/message_sender'
import { YCloudEventDispatcher } from './y_cloud_event_dispatch'
import { PrismaMongoClient } from '../../helper/mongodb/prisma'
import { exec } from 'child_process'

const app = express()
app.use(express.json())

Config.setting.dbName = 'moer_overseas'

/**
 * 墨尔海外事件转发
 */
app.post('/moer/event', async (req, res) => {
  // 接收消息
  const data: IMoerEvent = req.body

  MoerEventForwardHandler.handle(data) // 处理 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})


// 对回调事件进行统一处理
app.post('/ycloud/message', async (req, res) => {
  // 接收消息
  const data:Event = req.body
  if (data.type != 'whatsapp.inbound_message.received' || !data.whatsappInboundMessage?.from || !data.whatsappInboundMessage?.to) {
    logger.error('消息类型不对')
    res.send('ok')
    return
  }
  await YCloudEventDispatcher.dispatchMessage(data, data.whatsappInboundMessage.to)

  res.send('ok')
})

// 对消息发送失败。进行报警处理
app.post('/ycloud/sendResult', async (req, res) => {
  // 接收消息
  const data:Event = req.body
  if (data.type != 'whatsapp.message.updated' || !data.whatsappMessage?.from || !data.whatsappMessage?.to) {
    logger.error('消息类型不对')
    res.send('ok')
    return
  }
  const chat_id = `${data.whatsappMessage.to}_${data.whatsappMessage.from}`

  const status = data.whatsappMessage.status
  if (status == 'sent' || status == 'delivered' || status == 'read' || status == 'accepted') {
    const externalId = data.whatsappMessage.externalId
    const mongodbClient = PrismaMongoClient.getInstance()
    await mongodbClient.chat_history.updateMany({ where:{ message_id:externalId }, data:{ state:status } })
    logger.trace('消息状态修改', data, chat_id)
  } else {
    logger.error('消息发送失败', data, chat_id)
  }

  res.send('ok')
})


app.post('/webhook', async (req, res) => {
  const payload = req.body
  console.log(JSON.stringify(payload, null, 4))

  // 检查是否为开发分支推送
  if (payload.ref !== 'refs/heads/develop') {
    return res.status(200).json({ message: 'Not moer branch push, ignoring.' })
  }

  // 执行部署脚本
  try {
    // 使用 Promise 封装异步子进程执行
    const runCommand = (command: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            reject(error)
            return
          }
          resolve(stdout)
        })
      })
    }

    await runCommand('git pull')
    await runCommand('npm install')
    await runCommand('npm run prisma generate')
    await runCommand('fuser -k 4003/tcp')

    res.status(200).json({ message: 'Deployment script executed successfully.' })
  } catch (error) {
    logger.error(error)
    res.status(500).json({
      message: 'Deployment failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})



const serverPort = 4003


catchGlobalError()

app.listen(serverPort, '0.0.0.0', () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务