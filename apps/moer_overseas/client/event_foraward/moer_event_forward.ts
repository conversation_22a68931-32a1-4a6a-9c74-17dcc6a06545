import { IMoerEvent } from '../client_server'
import axios from 'axios'
import { ClientAccountConfig } from 'service/database/config'
import { DateHelper } from 'lib/date/date'
import { ObjectUtil } from 'lib/object'
import { Retry } from 'lib/retry/retry'
import { StringHelper } from 'lib/string'
import logger from 'model/logger/logger'
import { DataService } from '../../helper/getter/get_data'
import { MoerOverseasAPI } from 'model/moer_overseas_api/moer_overseas_api'
import { IEventType } from '../../logger/event_type'
import { PrismaMongoClient } from '../../helper/mongodb/prisma'
import { eventTrackClient } from '../../service/instance'


interface LiveStreamLog {
    channelId: string          // 频道号
    groupId?: string           // 分组id，非必传
    viewerId: string           // 参会人ID
    nickName?: string          // 客户昵称
    logType: number            // 日志类型
    interactType: string       // 日志类型对应枚举
    logTime: number            // 日志时间戳
    ipAddress?: string         // IP地址，非必传
    userAgent?: string         // UA信息，非必传
    referer?: string           // referer信息 - 请求来源，非必传
    viewerCount: number        // 当前参会人数
    timestamp: number          // 13位毫秒级时间戳
    sessionId: string | null   // 当前场次
    userOrigin?: string        // 客户来源，非必传
    content?: string           // logType为101和102时，表示学员名单的oss地址
    role: 'teacher' | 'guest' | 'viewer' | 'assistant' | 'attendee' | 'listener' // 客户身份信息
    inClass?: number           // 频道是否在直播中，1表示正在直播，2表示不在直播
    status: number             // 状态码（可能表示成功或失败的标志）
    websocketId: string        // websocket连接id，用于计算参与直播时长
}

interface ILiveStreamEvent {
    data: LiveStreamLog[]      // 日志数据列表
    logid: string              // 日志ID
    event: string              // 事件类型
}

export interface EnergyTestEvent {
    logid: string
    examScore: number
    userId: number | string
    mobile: string
    event: string

    project?: 'mainland' | 'international' // 国内/国际
}

export interface PaymentEvent {
    sku: string
    userId: number | string
    event: string

    project?: 'mainland' | 'international' // 国内/国际
}

interface IBindWechatUserEvent {
    logid: string
    event: string
    externalUserID: string
    name: string
    unionid: string
    avatar: string
    createdAt: string
    userId: number
    mobile: string

    project?: 'mainland' | 'international' // 国内/国际
}


interface IRumengyingOrderEvent {
    logid: string // 日志 ID
    event: string // 事件类型
    userId: number // 客户 ID
    mobile: string // 手机号
    nationcode: string
    goodsName: string // 商品名称
    sku: string // 商品 SKU
    stage: number // 阶段
    simpleName: string // 老师名称
    simpleId: number // 老师 ID
    language: string //偏好语言
}

interface IMergeUserEvent {
  event: string
  oldUserId: string
  oldMobile: string
  newUserId: string
  newMobile: string
  project?: 'mainland' | 'international' // 国内/国际
}



/**
 * 只做转发，不做事件处理
 */
export class MoerEventForwardHandler {
  private static moerIdMap: Map<string, string> | undefined

  private static async forwardEventByMoerId(event: IMoerEvent, moerId: string) {
    try {
      const accountId = await DataService.getAccountIdByMoerId(moerId)
      if (!accountId) {
        return
      }

      const serverAddress = await this.getServerAddress(accountId)

      if (serverAddress) {
        await this.dispatchEventToServer(serverAddress, event)
      }
    } catch (e) {
      console.error(`Forward event to server error: ${e}`)
    }
  }

  private static async forwardEventByAccountId(event: IMoerEvent, accountId: string) {
    try {
      const serverAddress = await this.getServerAddress(accountId)

      if (serverAddress) {
        await this.dispatchEventToServer(serverAddress, event)
      }
    } catch (e) {
      console.error(`Forward event to server error: ${e}`)
    }
  }

  private static async getServerAddress(wechatId: string) {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(wechatId)

    if (!serverAddress) {
      return null
    }

    return serverAddress
  }


  public static async handle(event: IMoerEvent) {
    if (ObjectUtil.isEmptyObject(event)) {
      // 忽略空事件
      return
    }

    logger.log(JSON.stringify(event, null, 4))

    try {
      switch (event.event) {
        case 'course_study_guide':
          this.handlePreCourseComplete(event)
          break

        case 'jinshuju_user_exam_score':
          this.handleCompleteEnergyTest(event as EnergyTestEvent)
          break

        case 'course_study_review':
          this.handleCourseComplete(event)
          break

        case 'course_pay_paid':
          this.handlePaidCourse(event as PaymentEvent)
          break

        case 'course_pay_unpaid':
          this.handlePaymentFailure(event as PaymentEvent)
          break

        case 'live_stream_status':
          this.handleLiveStreamStatus(event as ILiveStreamEvent)
          break

        case 'rumengying_order':
          this.handleRumenyingOrder(event as IRumengyingOrderEvent)
          break

        case 'merge_user':
          this.handleMergeUser(event as IMergeUserEvent)
          break

        default:
          this.handleUnknownEvent(event)
          break
      }
    } catch (e) {
      logger.error('moer event handler error:', e)
    }
  }

  static async handlePreCourseComplete(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const { trackId, courseNo } = await this.getCourseNo(moerId)
      eventTrackClient.track(trackId, IEventType.PreCourseComplete, {
        more_id: moerId,
        course_no: courseNo,
        class_day: 0,
      })
    } catch (error) {
      logger.error('Error in log of handlePreCourseComplete:', error)
    }
  }

  public static async handleCompleteEnergyTest(event: EnergyTestEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const { trackId, courseNo } = await this.getCourseNo(moerId)
      eventTrackClient.track(trackId, IEventType.EnergyTestScore, {
        more_id: moerId,
        course_no: courseNo,
        test_score: event?.examScore
      })
    } catch (error) {
      logger.error('Error in log of handleCompleteEnergyTest:', error)
    }
  }

  public static async handleCourseComplete(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)

    try {
      const vodId = event.vodId
      const { trackId, courseNo, classDay, liveId } = await this.getCourseNo(moerId, vodId)
      eventTrackClient.track(trackId, IEventType.CourseComplete, {
        more_id: moerId,
        course_no: courseNo,
        class_day: classDay,
        live_id: liveId,
      })
    } catch (error) {
      logger.error('Error in log of handleCourseComplete:', error)
    }
  }

  public static async handlePaidCourse(event: PaymentEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const chatId = await DataService.getChatIdByMoerId(moerId)
      const trackId: string = chatId ? chatId : moerId
      const sku = event.sku
      eventTrackClient.track(trackId, IEventType.PaymentComplete, {
        more_id: moerId,
        sku: sku,
      })
    } catch (error) {
      logger.error('Error in log of handlePaidCourse:', error)
    }
  }

  public static async handlePaymentFailure(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const chatId = await DataService.getChatIdByMoerId(moerId)
      const trackId: string = chatId ? chatId : moerId
      const sku = event.sku
      eventTrackClient.track(trackId, IEventType.PaymentNotCompleted, {
        more_id: moerId,
        sku: sku,
      })
    } catch (error) {
      logger.error('Error in track of handlePaymentFailure:', error)
    }
  }

  public static async handleLiveStreamStatus(event: ILiveStreamEvent) {
    for (const liveStreamData of event.data) {
      const moerId = liveStreamData.viewerId
      if (moerId && StringHelper.isNumber(moerId) && (await this.getCurrentMoerIdMap()).get(moerId)) {
        await this.forwardEventByMoerId({
          event: event.event,
          ...liveStreamData
        }, moerId)
      }
    }

    this.moerIdMap = undefined // 有可能有新增客户，这里每次调用之后重置掉
    try {
      const class_day = DateHelper.getWeekDay()
      for (const liveStreamData of event.data) {
        const moerId = liveStreamData.viewerId
        const { trackId, courseNo } = await this.getCourseNo(moerId)
        eventTrackClient.track(trackId, IEventType.liveStatusChange, {
          more_id: moerId,
          course_no: courseNo,
          class_day: class_day,
          live_id: liveStreamData?.channelId,
          status: liveStreamData?.interactType,
          log_time: liveStreamData?.logTime,
          websocket_id: liveStreamData?.websocketId
        })
      }
    } catch (error) {
      logger.error('Error in track of liveStreamStatus:', error)
    }
  }

  private static async getCurrentMoerIdMap() {
    if (this.moerIdMap) {
      return this.moerIdMap
    }

    // TODO 只处理 入门营直播
    // 取出所有客户 moerId 并缓存一下
    const chatsWithMoerId = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        moer_id: {
          isSet: true
        }
      }
    })

    this.moerIdMap = new Map<string, string>()
    chatsWithMoerId.forEach((chat) => {
      if (chat) {
        (this.moerIdMap as Map<string, string>).set(chat.moer_id as string,  chat.id)
      }
    })

    return this.moerIdMap
  }

  public static async handleUnknownEvent(event: IMoerEvent) {
    const moerId = event.userId ? event.userId.toString() : ''
    if (moerId) {
      await this.forwardEventByMoerId(event, moerId)
    } else {
      logger.debug('unknown moer event without userId:', JSON.stringify(event))
    }
  }

  public static async getCourseNo(moerId: string, vodId?: number) {
    const chatId = await DataService.getChatIdByMoerId(moerId)
    let courseNo: number | null | undefined
    let classDay: number | undefined
    let liveId: number | undefined

    if (chatId) {
      courseNo = await DataService.getCourseNoByChatId(chatId)
    } else {
      const user = await MoerOverseasAPI.getUserById(moerId)
      courseNo = user?.data?.userGoodsSeries?.[0]?.stage ?? null
    }

    const trackId: string = chatId ? chatId : moerId

    if (vodId && typeof courseNo === 'number') {
      const courseInfo = await DataService.getCourseInfoByCourseNo(courseNo)
      classDay = courseInfo?.resource?.find((item) => item.vodId === vodId)?.day
      liveId = courseInfo?.resource?.find((item) => item.vodId === vodId)?.liveId
      return { trackId, courseNo, classDay, liveId }
    }
    return { trackId, courseNo }
  }

  private static async dispatchEventToServer(serverAddress: string, event: any) {
    if (!serverAddress) {
      console.error('没有找到对应的服务器地址')
      return
    }

    try {
      await Retry.retry(4, async () => {
        await axios.post(`${serverAddress}/moer/event`, event, { insecureHTTPParser: true })
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 2 * 60 * 1000  // 2分钟
          if (retryCount === 2) return 10 * 60 * 1000 // 10分钟
          if (retryCount === 3) return 30 * 60 * 1000 // 30分钟
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      logger.error('事件分发失败：', serverAddress, e)
    }
  }

  private static async handleRumenyingOrder(event: IRumengyingOrderEvent) {
    await PrismaMongoClient.getInstance().runmenying_order.create({
      data:{
        moer_id: event.userId.toString(),
        phone_number: `${event.nationcode}${event.mobile}`,
        nation_code: event.nationcode,
        mobile: event.mobile,
        language: event.language,
      }
    })

    // 全量线索埋点
    eventTrackClient.track(event.userId.toString(10), IEventType.BootcampOrder, event)

  }

  private static async handleMergeUser(event: IMergeUserEvent) {
    const moerId = event.oldUserId
    await this.forwardEventByMoerId(event, moerId)
  }

  static forwardByRegion(data: IMoerEvent) {
    const CHINA_MOER_EVENT_SERVER_ADDRESS = 'http://*************:4002'
    const INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS = 'http://*************:4003'

    if (data.project && typeof data.project === 'string') {
      if (data.project === 'international') {
        this.dispatchEventToServer(INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS, data)
      } else {
        this.dispatchEventToServer(CHINA_MOER_EVENT_SERVER_ADDRESS, data)
      }
      return
    }

    // 其他事件两个服务都发送
    this.dispatchEventToServer(INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS, data)
    this.dispatchEventToServer(CHINA_MOER_EVENT_SERVER_ADDRESS, data)
  }
}