import { IActionInfo, MetaActionComponent } from 'model/meta_action/meta_action_component'
import { isScheduleTimeAfter } from '../../helper/tool/creat_schedule_task'
import { DataService } from '../../helper/getter/get_data'
import { MetaAction } from '../meta_action'


export class AfterCourse1 extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse1 = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })
    return Promise.resolve(afterCourse1)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaAction.metaActionAfterCourse1)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(MetaAction.thinkPromptAfterCourse1)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }


}