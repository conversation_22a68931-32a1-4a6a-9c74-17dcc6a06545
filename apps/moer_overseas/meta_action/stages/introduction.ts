import { IActionInfo, MetaActionComponent } from 'model/meta_action/meta_action_component'
import { PostAction } from './post_action'
import { MetaAction } from '../meta_action'
import { FreeTalk } from '../../workflow/freetalk'
import { chatStateStoreClient } from '../../service/instance'


export class Introduction extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const nodeCount = await chatStateStoreClient.getNodeCount(chatId, FreeTalk.name)

    return Promise.resolve(nodeCount <= 15)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaAction.metaActionIntroduction)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(MetaAction.thinkPromptIntroduction)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}